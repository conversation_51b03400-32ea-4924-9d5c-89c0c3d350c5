"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { Caveat } from "next/font/google";

const caveat = Caveat({ subsets: ["latin"], weight: ["400", "700"] });

export default function NavigatorPage() {
  const [hoveredSide, setHoveredSide] = useState<'therapist' | 'client' | null>(null);


  return (
    <div className="min-h-screen bg-white flex flex-col">
      {/* Header with Logo */}
      <header className="flex justify-center pt-8 pb-4">
        <Image
          src="/assets/images/client/client-logo.svg"
          alt="Thought Pudding Logo"
          width={191}
          height={79}
          className="object-contain w-[150px] h-[62px] md:w-[170px] md:h-[70px] lg:w-[191px] lg:h-[79px]"
          priority
        />
      </header>

      {/* Split Screen Container - Desktop: Side by side, Mobile: Stacked vertically */}
      <div className="flex-1 flex md:flex-row flex-col">
        {/* Client Side - Shows first on mobile, first on desktop (left side) */}
        <Link
          href="/clients"
          className={`md:flex-1 flex-[2] md:order-1 order-1 relative overflow-hidden transition-all duration-500 ease-in-out ${
            hoveredSide === 'therapist' ? 'md:flex-[0.3]' : hoveredSide === 'client' ? 'md:flex-[0.7]' : 'md:flex-1'
          }`}
          onMouseEnter={() => setHoveredSide('client')}
          onMouseLeave={() => setHoveredSide(null)}
        >
          {/* Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#FDF3DD] to-[#F5E6B8] opacity-90" />

          {/* Content - Centered vertically */}
          <div className="relative z-10 h-full flex flex-col justify-center items-center p-8 pt-24">
            <div className="text-center">
              <h1 className={`text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 md:mb-8 text-[#6F58A5] ${caveat.className}`}>
                I&apos;m here looking for therapy
              </h1>
              <p className="text-base md:text-lg lg:text-xl xl:text-2xl mb-8 md:mb-12 max-w-md text-black">
                Find the right therapist for your journey
              </p>

              {/* Begin Button */}
              <button className="bg-[#E9FA6F] text-black px-6 py-3 md:px-8 md:py-4 rounded-lg text-base md:text-lg font-medium hover:bg-[#d4e559] transition-colors">
                Begin Your Journey
              </button>
            </div>

            {/* Hover indicator - Hidden on mobile */}
            <div className={`hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-opacity duration-300 ${
              hoveredSide === 'client' ? 'opacity-100' : 'opacity-0'
            }`}>
              <div className="bg-black/20 backdrop-blur-sm rounded-full px-4 py-2">
                <span className="text-sm font-medium text-black">Click to Continue</span>
              </div>
            </div>
          </div>
        </Link>

        {/* Divider Line - Horizontal on mobile, vertical on desktop */}
        <div className="md:w-px md:h-auto w-full h-px bg-gray-200 relative z-20" />

        {/* Therapist Side - Shows second on mobile, second on desktop (right side) */}
        <Link
          href="/therapist"
          className={`md:flex-1 flex-1 md:order-2 order-2 relative overflow-hidden transition-all duration-500 ease-in-out ${
            hoveredSide === 'client' ? 'md:flex-[0.3]' : hoveredSide === 'therapist' ? 'md:flex-[0.7]' : 'md:flex-1'
          }`}
          onMouseEnter={() => setHoveredSide('therapist')}
          onMouseLeave={() => setHoveredSide(null)}
        >
          {/* Background Gradient */}
          <div className="absolute inset-0 bg-gradient-to-br from-[#6F58A5] to-[#6F58A5] opacity-90" />

          {/* Content */}
          <div className="relative z-10 h-full flex flex-col justify-center items-center text-white pt-16 p-8">
            <div className="text-center">
              <h1 className={`text-3xl md:text-4xl lg:text-5xl xl:text-6xl font-bold mb-6 md:mb-8 ${caveat.className}`}>
                I&apos;m here as a therapist
              </h1>
              <p className="text-base md:text-lg lg:text-xl xl:text-2xl mb-8 md:mb-12 max-w-md">
                Manage your private practice with ease
              </p>

              {/* Begin Button */}
              <button className="bg-[#E9FA6F] text-black px-6 py-3 md:px-8 md:py-4 rounded-lg text-base md:text-lg font-medium hover:bg-[#d4e559] transition-colors">
                Begin Your Journey
              </button>
            </div>

            {/* Hover indicator - Hidden on mobile */}
            <div className={`hidden md:block absolute bottom-8 left-1/2 transform -translate-x-1/2 transition-opacity duration-300 ${
              hoveredSide === 'therapist' ? 'opacity-100' : 'opacity-0'
            }`}>
              <div className="bg-white/20 backdrop-blur-sm rounded-full px-4 py-2">
                <span className="text-sm font-medium">Click to Continue</span>
              </div>
            </div>
          </div>
        </Link>
      </div>

      {/* Footer */}
      <footer className="py-6 text-center text-gray-600">
        <p className="text-sm">
          Choose your path to get started with Thought Pudding
        </p>
      </footer>
    </div>
  );
} 