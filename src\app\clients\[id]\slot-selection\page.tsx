"use client";

import { useEffect, useState, useCallback } from "react";
import {
  getPublicTherapistById,
  getTherapistWorkingHours,
  TherapistData,
  WorkingHoursSlot,
  RegularWorkingHours,
  SpecificWorkingHours,
  ConflictSchedule,
} from "@/services/public-calendar.service";
import Image from "next/image";
import { Roboto } from "next/font/google";
import "../fonts.css";
import { useRouter } from "next/navigation";

// Using types from our types file
import SessionModeSelector from "@/components/slot-selection/SessionModeSelector";
import SessionFeesSelector from "@/components/slot-selection/SessionFeesSelector";
import DateSelector from "@/components/slot-selection/DateSelector";
import TimeSlotSelector from "@/components/slot-selection/TimeSlotSelector";
import TherapistProfileBlock from "@/components/slot-selection/TherapistProfileBlock";
import CalendarModal from "@/components/slot-selection/CalendarModal";
import ProceedButton from "@/components/slot-selection/ProceedButton";

const roboto = Roboto({
  weight: ["400", "500", "700"],
  subsets: ["latin"],
  display: "swap",
});

type TherapistProfileData = {
  _id: string;
  identifier: string;
  name: string;
  fullName: string;
  pronouns: string;
  profileImage: string;
  profilePicUrl: string;
  experience: string;
  yearsOfExperience: number;
  location: string;
  designation: string;
  bookingMessage: string;
  minFee: number;
  maxFee: number;
  languages?: string[];
};

// Define the verification details interface to match the API response
interface VerificationDetails {
  docs?: string[];
  featuresNeed?: string[];
  genderPreference?: string[];
  practicingTitle?: string;
  sentForVerification?: boolean;
  source?: string[];
  uploadedDocsCount?: number;
  yearsOfExperience?: string;
}

type SessionMode = {
  name: string;
  duration: string;
  selected: boolean;
  slotType?: string;
};

export default function SlotSelectionPage({
  params,
}: {
  params: { id: string };
}) {
  const [therapistData, setTherapistData] =
    useState<TherapistProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [sessionModes, setSessionModes] = useState<SessionMode[]>([]);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [availableTimeSlots, setAvailableTimeSlots] = useState<
    WorkingHoursSlot[]
  >([]);
  const [workingHours, setWorkingHours] = useState<{
    regularWorkingHours: RegularWorkingHours[];
    specificWorkingHours: SpecificWorkingHours[];
    conflicts?: ConflictSchedule[];
  } | null>(null);

  // Store working hours for both session types to enable cross-checking
  const [allSessionTypesWorkingHours, setAllSessionTypesWorkingHours] = useState<{
    introductory?: {
      regularWorkingHours: RegularWorkingHours[];
      specificWorkingHours: SpecificWorkingHours[];
      conflicts?: ConflictSchedule[];
    };
    consultancy?: {
      regularWorkingHours: RegularWorkingHours[];
      specificWorkingHours: SpecificWorkingHours[];
      conflicts?: ConflictSchedule[];
    };
  }>({});
  const [isLoadingSlots, setIsLoadingSlots] = useState(false);
  const [datesWithSlots, setDatesWithSlots] = useState<Set<string>>(new Set());
  const router = useRouter();

  const { id } = params;

  // Helper function to format session mode display with dynamic durations
  const formatSessionMode = (sessionMode: string, allSessionModes: SessionMode[] = []): string => {
    if (!sessionMode) return sessionMode;

    // Extract the base session type (without duration)
    const baseSessionType = sessionMode.toLowerCase();

    // Group session modes by type and collect their durations
    const getSessionDurations = (type: string) => {
      const matchingModes = allSessionModes.filter(mode => {
        const modeName = mode.slotType?.toLowerCase() || mode.name.toLowerCase();
        return modeName.includes(type);
      });

      const durations = matchingModes.map(mode => {
        if (mode.slotType) {
          const parts = mode.slotType.split(" - ");
          if (parts.length > 1) {
            const durationPart = parts[1];
            // Extract number from duration (e.g., "50min" -> "50")
            const durationMatch = durationPart.match(/(\d+)/);
            return durationMatch ? parseInt(durationMatch[1]) : null;
          }
        }
        // Fallback to duration field
        const durationMatch = mode.duration.match(/(\d+)/);
        return durationMatch ? parseInt(durationMatch[1]) : null;
      }).filter(duration => duration !== null).sort((a, b) => a - b);

      return Array.from(new Set(durations)); // Remove duplicates and sort
    };

    // Handle introductory sessions
    if (baseSessionType.includes("introductory")) {
      const durations = getSessionDurations("introductory");
      if (durations.length > 0) {
        return "Introductory session";
      }
      return "Introductory session"; // fallback
    }

    // Handle consultation sessions
    if (baseSessionType.includes("consultation")) {
      const durations = getSessionDurations("consultation");
      if (durations.length > 15) {
        return "Consultation session";
      }
      return "Consultation session"; // fallback
    }

    // For any other session types, return as is
    return sessionMode;
  };

  // Clear booking completed flag when the slot selection page loads
  useEffect(() => {
    // Clear the bookingCompleted flag to ensure a fresh booking flow
    localStorage.removeItem("bookingCompleted");
  }, []);

  useEffect(() => {
    const fetchTherapistData = async () => {
      try {
        setIsLoading(true);

        // Use the public endpoint that doesn't require authentication
        let response;
        try {
          // Make the API call to get the therapist data
          response = await getPublicTherapistById(id);

          // Check if the response is valid
          if (!response || typeof response !== "object") {
            console.error("Invalid API response:", response);
            setError(
              "Unable to load therapist profile. The API returned an invalid response."
            );
            setIsLoading(false);
            return;
          }
        } catch (fetchError) {
          console.error("Error fetching therapist profile data:", fetchError);
          setError(
            "Unable to load therapist profile. Please check the identifier and try again."
          );
          setIsLoading(false);
          return;
        }

        const apiData = response as unknown as TherapistData & {
          verificationDetails?: VerificationDetails;
        };

        // Helper functions to safely handle potentially undefined or null values
        const safeString = (
          value: string | Record<string, unknown> | null | undefined
        ): string => {
          if (typeof value === "string") return value;
          return "";
        };

        const safeNumber = (
          value: number | string | null | undefined
        ): number => {
          if (typeof value === "number") return value;
          if (typeof value === "string") {
            const parsed = parseFloat(value);
            return isNaN(parsed) ? 0 : parsed;
          }
          return 0;
        };

        const safeStringArray = (
          value: string[] | null | undefined
        ): string[] => {
          if (Array.isArray(value)) return value;
          return [];
        };

        // Format location
        let locationValue = "";
        if (apiData.location) {
          if (Array.isArray(apiData.location)) {
            locationValue = apiData.location.join(", ");
          } else if (typeof apiData.location === "string") {
            locationValue = apiData.location;
          }
        }

        try {
          // Use the profile image URL from the API with better fallback logic
          let profileImageUrl = safeString(apiData.profilePicUrl);

          // If no profile image or it's not a valid URL, use static fallback
          if (!profileImageUrl || profileImageUrl === 'null' || profileImageUrl === 'undefined') {
            profileImageUrl = "/assets/images/newHome/therapist-profile-logo.png";
          } else if (!profileImageUrl.startsWith('http')) {
            // If it's a relative path, make it absolute
            const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://app.thoughtpudding.com';
            profileImageUrl = profileImageUrl.startsWith('/') ? `${baseUrl}${profileImageUrl}` : `${baseUrl}/${profileImageUrl}`;
          }

          // Check if verificationDetails exists and contains the required fields
          const verificationDetails = apiData.verificationDetails || {};
          const practicingTitle = verificationDetails?.practicingTitle;
          const yearsOfExperience = verificationDetails?.yearsOfExperience;

          const formattedData: TherapistProfileData = {
            _id: apiData._id || "",
            identifier: safeString(apiData.identifier),
            name: safeString(apiData.name),
            fullName: safeString(apiData.name),
            pronouns: safeString(apiData.pronouns),
            profileImage: profileImageUrl,
            profilePicUrl: profileImageUrl,
            experience:
              yearsOfExperience?.toString() ||
              apiData.yearsOfExperience?.toString() ||
              "0",
            yearsOfExperience:
              safeNumber(yearsOfExperience) ||
              safeNumber(apiData.yearsOfExperience),
            location: locationValue,
            designation:
              safeString(practicingTitle) || safeString(apiData.designation),
            minFee: safeNumber(apiData.minFee),
            maxFee: safeNumber(apiData.maxFee),
            bookingMessage: safeString(apiData.bookingMessage),
            languages: safeStringArray(apiData.languages),
          };
          setTherapistData(formattedData);

          // Process slotType data from API response
          if (
            apiData.slotType &&
            Array.isArray(apiData.slotType) &&
            apiData.slotType.length > 0
          ) {
            // First, create the session types without formatting to get all data
            const sessionTypesRaw = apiData.slotType.map((type, index) => {
              const parts = type.split(" - ");
              const name = parts[0] || type;
              const duration = parts[1] || "20min";

              return {
                name: name,
                duration: duration,
                selected: index === 0,
                slotType: type,
              };
            });

            // Now format the names with access to all session modes
            const sessionTypes = sessionTypesRaw.map(sessionType => ({
              ...sessionType,
              name: formatSessionMode(sessionType.name, sessionTypesRaw),
            }));

            setSessionModes(sessionTypes);

            if (sessionTypes.length > 0 && sessionTypes[0].slotType) {
              setTimeout(() => {
                fetchWorkingHours(sessionTypes[0].slotType);
              }, 100);
            }
          } else {
            // First create raw default session modes
            const defaultSessionModesRaw = [
              {
                name: "Introductory Call",
                duration: "20min",
                selected: true,
                slotType: "Introductory Call - 20min",
              },
              {
                name: "Consultation Call",
                duration: "50min",
                selected: false,
                slotType: "Consultation Call - 50min",
              },
            ];

            // Format the names with access to all session modes
            const defaultSessionModes = defaultSessionModesRaw.map(sessionType => ({
              ...sessionType,
              name: formatSessionMode(sessionType.name, defaultSessionModesRaw),
            }));

            setSessionModes(defaultSessionModes);

            setTimeout(() => {
              fetchWorkingHours(defaultSessionModes[0].slotType);
            }, 100);
          }
        } catch (formattingError) {
          console.error("Error formatting therapist data:", formattingError);
          setError("Unable to process therapist profile data.");
        }
      } catch (error) {
        console.error("Error in therapist data processing:", error);
        setError("Unable to load therapist profile. Please try again later.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchTherapistData();
  }, [id]);

  const handleSessionModeSelect = (index: number) => {
    const updatedModes = sessionModes.map((mode, i) => ({
      ...mode,
      selected: i === index,
    }));
    setSessionModes(updatedModes);

    // Fetch working hours for the selected session mode
    const selectedMode = updatedModes[index];
    if (selectedMode && selectedMode.slotType) {
      if (selectedDate) {
        fetchWorkingHours(selectedMode.slotType, selectedDate);
      } else {
        fetchWorkingHours(selectedMode.slotType);
      }
    }

    if (!selectedDate) {
      setAvailableTimeSlots([]);
    }
  };

  // Calendar helper functions are now in the DateSelector component

  const handlePrevMonth = () => {
    setCurrentMonth((prev) => {
      const prevMonth = new Date(prev);
      prevMonth.setMonth(prevMonth.getMonth() - 1);
      return prevMonth;
    });
  };

  const handleNextMonth = () => {
    setCurrentMonth((prev) => {
      const nextMonth = new Date(prev);
      nextMonth.setMonth(nextMonth.getMonth() + 1);
      return nextMonth;
    });
  };

  // Function to fetch working hours for both session types
  const fetchWorkingHours = useCallback(async (slotType?: string, selectedDateToCheck?: Date) => {
    try {
      setIsLoadingSlots(true);
      if (!therapistData || !therapistData._id) {
        console.error("Therapist _id not available");
        setIsLoadingSlots(false);
        return;
      }

      const selectedMode = sessionModes.find((mode) => mode.selected);
      const slotTypeParam = slotType || selectedMode?.slotType || "";

      // Determine current session type
      let currentSessionType = '';
      if (slotTypeParam.toLowerCase().includes('introductory')) {
        currentSessionType = 'introductory';
      } else {
        currentSessionType = 'consultancy';
      }

      // Fetch working hours for the current session type
      const response = await getTherapistWorkingHours(
        therapistData._id,
        slotTypeParam
      );

      // Fetch working hours for the other session type for cross-checking
      const otherSessionType = currentSessionType === 'introductory' ? 'consultancy' : 'introductory';
      const otherSlotType = currentSessionType === 'introductory' ? 'Consultation Call - 50min' : 'Introductory Call - 20min';

      let otherSessionResponse;
      try {
        otherSessionResponse = await getTherapistWorkingHours(
          therapistData._id,
          otherSlotType
        );
      } catch (error) {
        console.warn(`Could not fetch ${otherSessionType} working hours for cross-checking:`, error);
        otherSessionResponse = null;
      }

      if (response.status === "success" && response.data) {
        setWorkingHours({
          ...response.data,
          conflicts: response.data.conflicts || []
        });

        // Store working hours for both session types
        setAllSessionTypesWorkingHours(prev => ({
          ...prev,
          [currentSessionType]: {
            ...response.data,
            conflicts: response.data.conflicts || []
          },
          ...(otherSessionResponse?.status === "success" && otherSessionResponse.data ? {
            [otherSessionType]: {
              ...otherSessionResponse.data,
              conflicts: otherSessionResponse.data.conflicts || []
            }
          } : {})
        }));

        const datesSet = new Set<string>();

        // Only add dates from regular working hours (these are available for booking)
        const regularHours = response.data.regularWorkingHours[0];
        if (regularHours) {
          const daysOfWeek = [
            "sunday",
            "monday",
            "tuesday",
            "wednesday",
            "thursday",
            "friday",
            "saturday",
          ];
          const today = new Date();
          const nextMonth = new Date(today);
          nextMonth.setMonth(nextMonth.getMonth() + 1);

          // Check each day in the next 30 days
          for (let i = 0; i < 30; i++) {
            const date = new Date(today);
            date.setDate(today.getDate() + i);
            const dayOfWeek = daysOfWeek[date.getDay()];

            // If there are slots for this day of the week, add the date
            if (
              regularHours[dayOfWeek as keyof RegularWorkingHours]?.length > 0
            ) {
              const formattedDate = formatDateForAPI(date);
              datesSet.add(formattedDate);
            }
          }
        }

        setDatesWithSlots(datesSet);

        // If a date was selected before fetching working hours, check for slots for that date
        if (selectedDateToCheck) {
          // Always check regular working hours first for available slots
          if (regularHours) {
            const dayOfWeek = [
              "sunday",
              "monday",
              "tuesday",
              "wednesday",
              "thursday",
              "friday",
              "saturday",
            ][selectedDateToCheck.getDay()];

            const slots = regularHours[
              dayOfWeek as keyof RegularWorkingHours
            ] as WorkingHoursSlot[];

            if (slots && slots.length > 0) {
              // Filter out slots that should be hidden due to overlaps
              const filteredSlots = slots.filter(slot =>
                !shouldHideSlot(slot, selectedDateToCheck, currentSessionType)
              );
              setAvailableTimeSlots(filteredSlots);
              setIsLoadingSlots(false);
              return;
            }
          }

          // If no regular working hours found, set empty slots
          setAvailableTimeSlots([]);
        }
      }
    } catch (error) {
      console.error("Error fetching working hours:", error);

      // Set empty working hours data as fallback
      setWorkingHours({
        regularWorkingHours: [],
        specificWorkingHours: [],
        conflicts: [],
      });

      // Clear dates with slots
      setDatesWithSlots(new Set());
    } finally {
      setIsLoadingSlots(false);
    }
  }, [therapistData, sessionModes, allSessionTypesWorkingHours]);

  // Format date for API (DD-MM-YYYY)
  const formatDateForAPI = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, "0");
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
  };

  // Convert 24-hour time to 12-hour AM/PM format
  const convertTo12Hour = (time24: string): string => {
    const [hours, minutes] = time24.split(":");
    const hour = parseInt(hours, 10);
    const ampm = hour >= 12 ? "PM" : "AM";
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  // Helper function to check if two time ranges overlap
  const timeRangesOverlap = (start1: string, end1: string, start2: string, end2: string): boolean => {
    // Convert time strings to minutes for easier comparison
    const timeToMinutes = (time: string): number => {
      const [hours, minutes] = time.split(':').map(Number);
      return hours * 60 + minutes;
    };

    const start1Minutes = timeToMinutes(start1);
    const end1Minutes = timeToMinutes(end1);
    const start2Minutes = timeToMinutes(start2);
    const end2Minutes = timeToMinutes(end2);

    // Check if ranges overlap: start1 < end2 && start2 < end1
    return start1Minutes < end2Minutes && start2Minutes < end1Minutes;
  };

  // Helper function to check if a slot should be hidden due to overlaps
  const shouldHideSlot = (slot: WorkingHoursSlot, selectedDate: Date, currentSessionType: string): boolean => {
    const formattedDate = formatDateForAPI(selectedDate);

    // Check if this regular working hour slot overlaps with any specific working hour slot of the same session type
    if (workingHours?.specificWorkingHours) {
      const specificDay = workingHours.specificWorkingHours.find(
        (day) => day.date === formattedDate
      );

      if (specificDay && specificDay.slots.length > 0) {
        // Check for exact match or any overlap with specific working hour slots
        const hasOverlapOrMatch = specificDay.slots.some(specificSlot =>
          // Exact match
          (specificSlot.startTime === slot.startTime && specificSlot.endTime === slot.endTime) ||
          // Any overlap
          timeRangesOverlap(slot.startTime, slot.endTime, specificSlot.startTime, specificSlot.endTime)
        );

        if (hasOverlapOrMatch) {
          return true; // Hide this slot because it overlaps with a specific working hour slot
        }
      }
    }

    // Cross-check with the other session type's specific working hours
    const otherSessionType = currentSessionType === 'introductory' ? 'consultancy' : 'introductory';
    const otherSessionWorkingHours = allSessionTypesWorkingHours[otherSessionType as keyof typeof allSessionTypesWorkingHours];

    if (otherSessionWorkingHours?.specificWorkingHours) {
      const otherSpecificDay = otherSessionWorkingHours.specificWorkingHours.find(
        (day) => day.date === formattedDate
      );

      if (otherSpecificDay && otherSpecificDay.slots.length > 0) {
        // Check for any overlap with the other session type's specific working hour slots
        const hasOverlapWithOtherType = otherSpecificDay.slots.some(specificSlot =>
          // Exact match
          (specificSlot.startTime === slot.startTime && specificSlot.endTime === slot.endTime) ||
          // Any overlap
          timeRangesOverlap(slot.startTime, slot.endTime, specificSlot.startTime, specificSlot.endTime)
        );

        if (hasOverlapWithOtherType) {
          return true; // Hide this slot because it overlaps with the other session type's specific working hour slot
        }
      }
    }

    return false;
  };

  // Utility function to check if a slot is booked based on conflicts only
  const isSlotBooked = (slot: WorkingHoursSlot, selectedDate: Date, conflicts?: ConflictSchedule[]): boolean => {
    const formattedDate = formatDateForAPI(selectedDate);
    const dayOfWeek = [
      "sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"
    ][selectedDate.getDay()];

    // Check conflicts for bookings
    if (!conflicts || conflicts.length === 0) return false;

    return conflicts.some(conflict => {
      // Check if this conflict applies to the current slot
      const slotMatches = conflict.conflictingSlot.startTime === slot.startTime &&
                         conflict.conflictingSlot.endTime === slot.endTime;

      if (!slotMatches) return false;

      // For specific date conflicts
      if (conflict.type === 'specific' && conflict.date === formattedDate) {
        return true;
      }

      // For regular day conflicts, check if the selected date matches any of the existing schedule dates
      if (conflict.type === 'regular' && conflict.day === dayOfWeek) {
        // Extract the date from existingSchedule.fromDate (format: "YYYY-MM-DD HH:mm")
        const existingScheduleDate = conflict.existingSchedule.fromDate.split(' ')[0]; // Get "YYYY-MM-DD" part

        // Convert selectedDate to YYYY-MM-DD format for comparison
        const selectedDateFormatted = selectedDate.getFullYear() + '-' +
          String(selectedDate.getMonth() + 1).padStart(2, '0') + '-' +
          String(selectedDate.getDate()).padStart(2, '0');

        // Only mark as booked if the selected date matches the specific existing schedule date
        return existingScheduleDate === selectedDateFormatted;
      }

      return false;
    });
  };

  const handleDateSelect = (date: Date) => {
    setSelectedDate(date);
    setSelectedTimeSlot(null);
    setIsLoadingSlots(true);

    if (workingHours) {
      // Always check regular working hours first for available slots
      const regularHours = workingHours.regularWorkingHours[0];
      if (regularHours) {
        const dayOfWeek = [
          "sunday",
          "monday",
          "tuesday",
          "wednesday",
          "thursday",
          "friday",
          "saturday",
        ][date.getDay()];
        const slots = regularHours[
          dayOfWeek as keyof RegularWorkingHours
        ] as WorkingHoursSlot[];

        if (slots && slots.length > 0) {
          // Determine current session type
          const selectedMode = sessionModes.find((mode) => mode.selected);
          const slotTypeParam = selectedMode?.slotType || "";
          let currentSessionType = '';
          if (slotTypeParam.toLowerCase().includes('introductory')) {
            currentSessionType = 'introductory';
          } else {
            currentSessionType = 'consultancy';
          }

          // Filter out slots that should be hidden due to overlaps
          const filteredSlots = slots.filter(slot =>
            !shouldHideSlot(slot, date, currentSessionType)
          );
          setAvailableTimeSlots(filteredSlots);
          setIsLoadingSlots(false);
          return;
        }
      }

      // If no regular working hours found, set empty slots
      setAvailableTimeSlots([]);
      setIsLoadingSlots(false);
    } else {
      fetchWorkingHours(undefined, date)
        .catch(() => {
          setIsLoadingSlots(false);
          setAvailableTimeSlots([]);
        });
    }
  };

  const formatDate = (date: Date) => {
    const options: Intl.DateTimeFormatOptions = {
      weekday: "short",
      day: "numeric",
      month: "short",
      year: "numeric",
    };
    return date.toLocaleDateString("en-US", options);
  };

  // State for selected time slot
  const [selectedTimeSlot, setSelectedTimeSlot] =
    useState<WorkingHoursSlot | null>(null);
  const [showCalendarModal, setShowCalendarModal] = useState(false);

  const handleTimeSlotSelect = (slot: WorkingHoursSlot) => {
    setSelectedTimeSlot(slot);
  };

  const handleDateSelectFromModal = (date: Date) => {
    setShowCalendarModal(false);
    handleDateSelect(date);
  };

  const handleProceed = () => {
    const selectedMode = sessionModes.find((mode) => mode.selected);

    if (!selectedMode) {
      alert("Please select a session mode before proceeding.");
      return;
    }

    if (!selectedDate) {
      alert("Please select a date for your session.");
      return;
    }

    if (!selectedTimeSlot) {
      alert("Please select a time slot for your session.");
      return;
    }

    if (!therapistData || !therapistData._id) {
      alert("Therapist information is not available. Please try again later.");
      return;
    }

    // Calculate session fee based on selected mode
    let sessionFee = 0;
    if (selectedMode.name.toLowerCase().includes("introductory")) {
      sessionFee = 0; // Set to 0 for introductory sessions
    } else {
      sessionFee = therapistData.maxFee;
    }

    // Create booking data object
    const bookingData = {
      therapistId: therapistData._id,
      therapistIdentifier: therapistData.identifier,
      therapistName: therapistData.name,
      therapistFullName: therapistData.fullName,
      therapistPronouns: therapistData.pronouns,
      therapistProfileImage: therapistData.profileImage,
      therapistExperience: therapistData.experience,
      therapistDesignation: therapistData.designation,
      sessionMode: selectedMode.name,
      sessionDuration: selectedMode.duration,
      slotType: selectedMode.slotType,
      date: formatDateForAPI(selectedDate),
      formattedDate: formatDate(selectedDate),
      startTime: selectedTimeSlot.startTime,
      endTime: selectedTimeSlot.endTime,
      duration: selectedTimeSlot.duration,
      sessionFee: sessionFee,
      minFee: therapistData.minFee,
      maxFee: therapistData.maxFee,
      bookingMessage: therapistData.bookingMessage,
      formattedStartTime: convertTo12Hour(selectedTimeSlot.startTime),
    };

    // Store booking data in localStorage
    localStorage.setItem("bookingData", JSON.stringify(bookingData));

    // Navigate to the booking confirmation page
    router.push(`/clients/${id}/booking-confirmation`);
  };

  if (isLoading) {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className} relative`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
          backgroundAttachment: "fixed",
        }}
      >
        <div className="flex justify-center mb-6">
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding Logo"
            width={150}
            height={40}
            className="h-auto"
          />
        </div>
        <div className="text-center">
          <div className="inline-block animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-white mb-4"></div>
          <p className="text-xl font-medium text-white">
            Loading therapist profile...
          </p>
        </div>
      </div>
    );
  }

  if (error || !therapistData) {
    return (
      <div
        className={`bg-[#2A1B6D] min-h-screen text-white flex flex-col items-center justify-center p-4 md:p-6 ${roboto.className} relative`}
        style={{
          backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
          backgroundSize: "cover",
          backgroundPosition: "center",
          backgroundBlendMode: "overlay",
          backgroundAttachment: "fixed",
        }}
      >
        <div className="text-black p-8 rounded-xl shadow-lg max-w-md w-full bg-white">
          <div className="flex justify-center mb-6">
            <Image
              src="/assets/images/newHome/therapist-profile-logo.png"
              alt="Thought Pudding Logo"
              width={150}
              height={40}
              className="h-auto"
            />
          </div>
          <div className="text-center">
            <h2 className="text-xl font-semibold text-red-600 mb-4">
              Unable to Load Profile
            </h2>
            <p className="text-black mb-6">
              {error ||
                "We couldn't find the therapist profile you're looking for. Please check the URL and try again."}
            </p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div
      className={`bg-[#2A1B6D] min-h-screen text-white p-4 md:p-6 ${roboto.className} relative`}
      style={{
        backgroundImage: `url('/assets/images/newHome/bg-carousal.png')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundBlendMode: "overlay",
        backgroundAttachment: "fixed",
      }}
    >

      {/* Main Container with all content */}
      <div className="w-full mx-auto md:max-w-[1400px] mb-8 md:mt-16 md:mx-auto">
        {/* Mobile Logo - Only visible on mobile at top left */}
        <div className="flex justify-start mt-0 mb-4 items-center sm:hidden">
          <p className="text-sm text-white mr-2">Powered by</p>
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding"
            width={100}
            height={25}
            className="h-auto"
          />
        </div>

        {/* Mobile Layout */}
        <div
          className="block md:hidden w-full rounded-xl border border-gray-200 overflow-hidden"
          style={{
            backgroundColor: "white",
            backgroundImage: "url('/assets/images/newHome/bg-home.png')",
            backgroundSize: "auto 100px",
            backgroundPosition: "center",
            backgroundRepeat: "repeat",
            backgroundBlendMode: "multiply",
          }}
        >
          {/* Mobile Therapist Profile */}
          <TherapistProfileBlock therapistData={therapistData} isMobile={true} />

          {/* Mobile Session Selection */}
          <div className="bg-white p-4">
            {/* Step 1: Select Session Mode - Column Layout */}
            <SessionModeSelector
              sessionModes={sessionModes}
              onSessionModeSelect={handleSessionModeSelect}
              isMobile={true}
            />

            {/* Step 2: Select Session Fees - Single Row */}
            <SessionFeesSelector
              minFee={therapistData.minFee}
              maxFee={therapistData.maxFee}
              selectedSessionMode={sessionModes.find(mode => mode.selected)?.name}
              isMobile={true}
            />

            {/* Step 3: Select The Date */}
            <DateSelector
              selectedDate={selectedDate}
              currentMonth={currentMonth}
              datesWithSlots={datesWithSlots}
              onDateSelect={handleDateSelect}
              onPrevMonth={handlePrevMonth}
              onNextMonth={handleNextMonth}
              formatDateForAPI={formatDateForAPI}
              isMobile={true}
              onOpenCalendarModal={() => setShowCalendarModal(true)}
            />

            {/* Calendar Modal */}
            <CalendarModal
              showCalendarModal={showCalendarModal}
              onClose={() => setShowCalendarModal(false)}
              currentMonth={currentMonth}
              onPrevMonth={handlePrevMonth}
              onNextMonth={handleNextMonth}
              selectedDate={selectedDate}
              datesWithSlots={datesWithSlots}
              formatDateForAPI={formatDateForAPI}
              onDateSelect={handleDateSelectFromModal}
            />

            {/* Mobile Time Slots */}
            {selectedDate && (
              <TimeSlotSelector
                selectedDate={selectedDate}
                availableTimeSlots={availableTimeSlots}
                selectedTimeSlot={selectedTimeSlot}
                onTimeSlotSelect={handleTimeSlotSelect}
                formatDate={formatDate}
                convertTo12Hour={convertTo12Hour}
                isLoadingSlots={isLoadingSlots}
                isMobile={true}
                isSlotBooked={(slot) => isSlotBooked(slot, selectedDate, workingHours?.conflicts)}
                selectedSessionMode={sessionModes.find(mode => mode.selected)?.name}
              />
            )}

            {/* Mobile Proceed Button */}
            <ProceedButton
              sessionModes={sessionModes}
              selectedDate={selectedDate}
              selectedTimeSlot={selectedTimeSlot}
              onProceed={handleProceed}
              isMobile={true}
            />
          </div>
        </div>

        {/* Desktop Layout */}
        <div
          className="hidden md:block w-full bg-white rounded-xl border border-gray-200 overflow-hidden"
          style={{
            backgroundImage: "url('/assets/images/newHome/bg-home.png')",
            backgroundSize: "contain",
            backgroundPosition: "center",
            backgroundRepeat: "repeat",
            backgroundBlendMode: "multiply",
            opacity: 0.9,
          }}
        >
          {/* Upper Section - Two Column Layout */}
          <div className="flex flex-col md:flex-row gap-6">
            {/* Left Section - Session Booking */}
            <div className="w-full md:w-2/3 bg-white rounded-xl shadow-md p-4 mx-2 my-[50px] ml-[50px]">
              <h1 className="text-xl font-bold mb-6 text-[#251D5C] gilmer-bold">
                SCHEDULE YOUR SESSION
              </h1>

              {/* Step 1: Select Session Mode */}
              <SessionModeSelector
                sessionModes={sessionModes}
                onSessionModeSelect={handleSessionModeSelect}
              />

              {/* Gray Divider Line */}
              <div className="w-full h-px bg-gray-300 mb-6"></div>

              {/* Step 2: Select Session Fees */}
              <SessionFeesSelector
                minFee={therapistData.minFee}
                maxFee={therapistData.maxFee}
                selectedSessionMode={sessionModes.find(mode => mode.selected)?.name}
              />
            </div>

            {/* Right Section - Therapist Profile */}
            <TherapistProfileBlock therapistData={therapistData} />
          </div>

          {/* Lower Section - Calendar and Slots - Desktop Only */}
          <div className="hidden md:block bg-white text-black p-5 mt-6 mx-[50px] rounded-xl">
            <h2 className="text-lg font-bold mb-6 text-[#251D5C] gilmer-bold">
              PICK THE DATES FOR YOUR CUSTOM TIME SLOTS
            </h2>

            {/* Calendar and Time Slots */}
            <div className="flex gap-6">
              {/* Calendar - Left Side (40% width) */}
              <DateSelector
                selectedDate={selectedDate}
                currentMonth={currentMonth}
                datesWithSlots={datesWithSlots}
                onDateSelect={handleDateSelect}
                onPrevMonth={handlePrevMonth}
                onNextMonth={handleNextMonth}
                formatDateForAPI={formatDateForAPI}
              />

              {/* Time slots - Right Side (60% width) */}
              <TimeSlotSelector
                selectedDate={selectedDate}
                availableTimeSlots={availableTimeSlots}
                selectedTimeSlot={selectedTimeSlot}
                onTimeSlotSelect={handleTimeSlotSelect}
                formatDate={formatDate}
                convertTo12Hour={convertTo12Hour}
                isLoadingSlots={isLoadingSlots}
                isSlotBooked={selectedDate ? (slot) => isSlotBooked(slot, selectedDate, workingHours?.conflicts) : undefined}
                selectedSessionMode={sessionModes.find(mode => mode.selected)?.name}
              />
            </div>

            {/* Proceed Button */}
            <ProceedButton
              sessionModes={sessionModes}
              selectedDate={selectedDate}
              selectedTimeSlot={selectedTimeSlot}
              onProceed={handleProceed}
            />
          </div>
        </div>

        {/* Footer Logo - Only visible on desktop */}
        <div className="hidden md:flex justify-end mt-4 mb-2 items-center">
          <p className="text-sm text-white mr-2">Powered by</p>
          <Image
            src="/assets/images/newHome/therapist-profile-logo.png"
            alt="Thought Pudding"
            width={120}
            height={30}
            className="h-auto"
          />
        </div>
      </div>
    </div>
  );
}
